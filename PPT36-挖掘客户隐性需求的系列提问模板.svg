<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 顶部装饰弧线 -->
  <path d="M 0 0 Q 960 80 1920 0 L 1920 120 Q 960 40 0 120 Z" fill="url(#headerGradient)" opacity="0.1"/>
  
  <!-- 标题 -->
  <text x="960" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#0066cc">价值引领：挖掘客户"未说出口"的隐性需求</text>
  
  <!-- 装饰线 -->
  <line x1="260" y1="200" x2="1660" y2="200" stroke="#0066cc" stroke-width="3"/>
  
  <!-- 什么是隐性需求 -->
  <g transform="translate(80, 240)">
    <rect x="0" y="0" width="1760" height="140" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <text x="30" y="35" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#333333">什么是隐性需求？为何重要？</text>
    
    <text x="30" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">定义：</text>
    <text x="100" y="70" font-family="Microsoft YaHei" font-size="18" fill="#666666">客户并未明确表达，甚至自己都未清晰意识到的潜在需求、期望或深层困扰</text>
    
    <text x="30" y="100" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">重要性：</text>
    <g transform="translate(100, 80)">
      <circle cx="15" cy="15" r="8" fill="#FF6B6B"/>
      <text x="40" y="22" font-family="Microsoft YaHei" font-size="18" fill="#666666">满足显性需求是"合格"，挖掘并满足隐性需求是"优秀"甚至"卓越"</text>
      
      <circle cx="15" cy="40" r="8" fill="#4ECDC4"/>
      <text x="40" y="47" font-family="Microsoft YaHei" font-size="18" fill="#666666">是实现差异化竞争、创造超预期价值、建立深度信任的关键</text>
    </g>
  </g>
  
  <!-- 挖掘隐性需求的五大提问方向 -->
  <g transform="translate(80, 400)">
    <text x="0" y="0" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">挖掘隐性需求的五大提问方向与话术模板</text>
    
    <!-- 方向一 -->
    <rect x="0" y="40" width="850" height="140" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <circle cx="40" cy="80" r="20" fill="#FF6B6B"/>
    <text x="40" y="88" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">1</text>
    <text x="80" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">基于现状，展望未来 - "登高望远"</text>
    <text x="80" y="95" font-family="Microsoft YaHei" font-size="16" fill="#666666">引导客户思考现有状况的局限性，以及如果这些局限被打破，</text>
    <text x="80" y="115" font-family="Microsoft YaHei" font-size="16" fill="#666666">未来可能会有哪些新的可能性</text>
    <text x="80" y="140" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="#0066cc">话术示例：</text>
    <text x="80" y="160" font-family="Microsoft YaHei" font-size="14" fill="#666666">"如果网络速度能比现在快5倍，您觉得这会对贵公司未来拓展哪些新的业务模式提供可能？"</text>
    
    <!-- 方向二 -->
    <rect x="870" y="40" width="850" height="140" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <circle cx="910" cy="80" r="20" fill="#4ECDC4"/>
    <text x="910" y="88" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">2</text>
    <text x="950" y="70" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">关注趋势，引发思考 - "见贤思齐"</text>
    <text x="950" y="95" font-family="Microsoft YaHei" font-size="16" fill="#666666">结合客户所在行业的发展趋势、竞争对手的动态或行业最佳实践，</text>
    <text x="950" y="115" font-family="Microsoft YaHei" font-size="16" fill="#666666">引导客户思考自身存在的差距和潜在的改进空间</text>
    <text x="950" y="140" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="#0066cc">话术示例：</text>
    <text x="950" y="160" font-family="Microsoft YaHei" font-size="14" fill="#666666">"我们看到同行业内的一些领先企业，已经开始利用高速稳定的网络实现了供应链的实时数据共享..."</text>
    
    <!-- 方向三 -->
    <rect x="0" y="200" width="850" height="140" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <circle cx="40" cy="240" r="20" fill="#45B7D1"/>
    <text x="40" y="248" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">3</text>
    <text x="80" y="230" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">深挖"问题背后的问题" - "刨根问底"</text>
    <text x="80" y="255" font-family="Microsoft YaHei" font-size="16" fill="#666666">当客户提出一个表面问题时，通过连续追问，探究问题产生的</text>
    <text x="80" y="275" font-family="Microsoft YaHei" font-size="16" fill="#666666">深层原因以及由此引发的一系列连锁反应和潜在影响</text>
    <text x="80" y="300" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="#0066cc">话术示例（客户抱怨"Wi-Fi信号不好"）：</text>
    <text x="80" y="320" font-family="Microsoft YaHei" font-size="14" fill="#666666">"这种情况对员工的移动办公体验和客户的等候感受有什么影响？"</text>
    
    <!-- 方向四 -->
    <rect x="870" y="200" width="850" height="140" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <circle cx="910" cy="240" r="20" fill="#96CEB4"/>
    <text x="910" y="248" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="white">4</text>
    <text x="950" y="230" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">探索"未被满足的期望" - "描绘梦想"</text>
    <text x="950" y="255" font-family="Microsoft YaHei" font-size="16" fill="#666666">引导客户思考在现有条件下无法实现，但内心深处期望达成的</text>
    <text x="950" y="275" font-family="Microsoft YaHei" font-size="16" fill="#666666">目标或愿景</text>
    <text x="950" y="300" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="#0066cc">话术示例：</text>
    <text x="950" y="320" font-family="Microsoft YaHei" font-size="14" fill="#666666">"如果让您畅想一下未来理想的智慧办公环境，您希望团队之间的协作能够达到怎样一种状态？"</text>
  </g>
  
  <!-- 方向五 -->
  <g transform="translate(80, 760)">
    <rect x="0" y="0" width="1760" height="120" rx="15" fill="#0066cc" opacity="0.05" stroke="#0066cc" stroke-width="2"/>
    <circle cx="50" cy="60" r="20" fill="#FFEAA7"/>
    <text x="50" y="68" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" font-weight="bold" fill="#F39C12">5</text>
    <text x="90" y="45" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">"如果……将会怎样？"假设性提问 - "打开脑洞"</text>
    <text x="90" y="75" font-family="Microsoft YaHei" font-size="18" fill="#333333">通过设置一个理想化的情景，帮助客户打开思路，畅想可能性</text>
    <text x="90" y="100" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#0066cc">话术示例：</text>
    <text x="200" y="100" font-family="Microsoft YaHei" font-size="16" fill="#666666">"如果我们能为您提供一个永远在线、永不卡顿、绝对安全的'智慧网络大脑'，您觉得贵公司的业务运营效率会发生哪些颠覆性的改变？"</text>
  </g>
  
  <!-- 小组练习 -->
  <g transform="translate(80, 900)">
    <rect x="0" y="0" width="1760" height="120" rx="15" fill="#FFEAA7" opacity="0.2" stroke="#F39C12" stroke-width="2"/>
    <text x="30" y="35" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#F39C12">小组练习</text>
    <text x="30" y="65" font-family="Microsoft YaHei" font-size="22" fill="#333333">针对一个"对现有网络还算满意，但对新技术持观望态度"的客户（如长沙某传统制造企业老板），</text>
    <text x="30" y="90" font-family="Microsoft YaHei" font-size="22" fill="#333333">各小组设计一套提问组合，尝试挖掘其在智能制造、数据分析、供应链优化等方面的隐性需求</text>
  </g>
  
  <!-- 右侧装饰图形 -->
  <g transform="translate(1650, 500)" opacity="0.4">
    <circle cx="0" cy="0" r="60" fill="none" stroke="#0066cc" stroke-width="3"/>
    <circle cx="-30" cy="-20" r="10" fill="#FF6B6B" opacity="0.6"/>
    <circle cx="30" cy="-20" r="10" fill="#4ECDC4" opacity="0.6"/>
    <circle cx="-30" cy="20" r="10" fill="#45B7D1" opacity="0.6"/>
    <circle cx="30" cy="20" r="10" fill="#96CEB4" opacity="0.6"/>
    <circle cx="0" cy="0" r="10" fill="#FFEAA7" opacity="0.6"/>
    <path d="M -30 -20 Q 0 -40 30 -20 Q 40 0 30 20 Q 0 40 -30 20 Q -40 0 -30 -20" stroke="#0066cc" stroke-width="2" fill="none"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="8" fill="white">隐性</text>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 0 960 Q 960 1000 1920 960 L 1920 1080 L 0 1080 Z" fill="#0066cc" opacity="0.05"/>
</svg>
