<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 顶部装饰弧线 -->
  <path d="M 0 0 Q 960 80 1920 0 L 1920 120 Q 960 40 0 120 Z" fill="url(#headerGradient)" opacity="0.1"/>
  
  <!-- 标题 -->
  <text x="960" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#0066cc">数据赋能：量化呈现方案价值，提升客户感知 (上)</text>
  
  <!-- 装饰线 -->
  <line x1="260" y1="200" x2="1660" y2="200" stroke="#0066cc" stroke-width="3"/>
  
  <!-- 为何要量化方案价值 -->
  <g transform="translate(80, 240)">
    <text x="0" y="0" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">为何要量化方案价值？——让价值"看得见、摸得着、算得清"</text>
    
    <g transform="translate(0, 40)">
      <!-- 增强可信度 -->
      <rect x="0" y="0" width="850" height="120" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <circle cx="40" cy="60" r="20" fill="#FF6B6B"/>
      <text x="40" y="68" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">可信</text>
      <text x="80" y="45" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">增强方案的可信度与说服力</text>
      <text x="80" y="70" font-family="Microsoft YaHei" font-size="18" fill="#666666">数字比空泛的描述更有力量</text>
      
      <!-- 直观理解 -->
      <rect x="870" y="0" width="850" height="120" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <circle cx="910" cy="60" r="20" fill="#4ECDC4"/>
      <text x="910" y="68" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">直观</text>
      <text x="950" y="45" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">帮助客户更直观地理解方案带来的实际效益</text>
      <text x="950" y="70" font-family="Microsoft YaHei" font-size="18" fill="#666666">从"感觉不错"到"确实能帮我省钱/赚钱/提效"</text>
      
      <!-- 决策支持 -->
      <rect x="0" y="140" width="850" height="120" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <circle cx="40" cy="200" r="20" fill="#45B7D1"/>
      <text x="40" y="208" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">决策</text>
      <text x="80" y="185" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">为客户的投资决策提供关键数据支持</text>
      <text x="80" y="210" font-family="Microsoft YaHei" font-size="18" fill="#666666">尤其是针对财务部门和高层决策者</text>
      
      <!-- 应对异议 -->
      <rect x="870" y="140" width="850" height="120" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <circle cx="910" cy="200" r="20" fill="#96CEB4"/>
      <text x="910" y="208" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">异议</text>
      <text x="950" y="185" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#333333">有效应对价格异议</text>
      <text x="950" y="210" font-family="Microsoft YaHei" font-size="18" fill="#666666">将讨论焦点从"投入多少成本"转移到"能获得多大回报"</text>
    </g>
  </g>
  
  <!-- 构建强有力的"价值证据链" -->
  <g transform="translate(80, 520)">
    <text x="0" y="0" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">构建强有力的"价值证据链"——让数据替你说话</text>
    
    <!-- 证据链核心构成 -->
    <rect x="0" y="40" width="1760" height="120" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
    <text x="30" y="70" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#333333">证据链核心构成（图示：链条环环相扣）：</text>
    
    <!-- 链条图示 -->
    <g transform="translate(100, 90)">
      <!-- 客户痛点量化 -->
      <rect x="0" y="0" width="120" height="40" rx="5" fill="#FF6B6B" opacity="0.8"/>
      <text x="60" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">客户痛点量化</text>
      
      <!-- 连接线 -->
      <line x1="120" y1="20" x2="150" y2="20" stroke="#0066cc" stroke-width="3"/>
      
      <!-- 解决方案特性 -->
      <rect x="150" y="0" width="120" height="40" rx="5" fill="#4ECDC4" opacity="0.8"/>
      <text x="210" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">解决方案特性</text>
      
      <!-- 连接线 -->
      <line x1="270" y1="20" x2="300" y2="20" stroke="#0066cc" stroke-width="3"/>
      
      <!-- 预期效益量化 -->
      <rect x="300" y="0" width="120" height="40" rx="5" fill="#45B7D1" opacity="0.8"/>
      <text x="360" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">预期效益量化</text>
      
      <!-- 连接线 -->
      <line x1="420" y1="20" x2="450" y2="20" stroke="#0066cc" stroke-width="3"/>
      
      <!-- 成功案例数据 -->
      <rect x="450" y="0" width="120" height="40" rx="5" fill="#96CEB4" opacity="0.8"/>
      <text x="510" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">成功案例数据</text>
      
      <!-- 连接线 -->
      <line x1="570" y1="20" x2="600" y2="20" stroke="#0066cc" stroke-width="3"/>
      
      <!-- 客户证言 -->
      <rect x="600" y="0" width="120" height="40" rx="5" fill="#FFEAA7" opacity="0.8"/>
      <text x="660" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="#F39C12">客户证言</text>
      
      <!-- 连接线 -->
      <line x1="720" y1="20" x2="750" y2="20" stroke="#0066cc" stroke-width="3"/>
      
      <!-- ROI/TCO分析 -->
      <rect x="750" y="0" width="120" height="40" rx="5" fill="#DDA0DD" opacity="0.8"/>
      <text x="810" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="12" font-weight="bold" fill="white">ROI/TCO分析</text>
    </g>
  </g>
  
  <!-- 数据来源的多样化与可信度提升策略 -->
  <g transform="translate(80, 700)">
    <text x="0" y="0" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">数据来源的多样化与可信度提升策略</text>
    
    <g transform="translate(0, 40)">
      <!-- 内部数据 -->
      <rect x="0" y="0" width="420" height="160" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <text x="30" y="30" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#FF6B6B">内部数据（联通自身）</text>
      <text x="30" y="55" font-family="Microsoft YaHei" font-size="16" fill="#333333">来源：网管系统、BSS/OSS系统、客户服务记录</text>
      <text x="30" y="75" font-family="Microsoft YaHei" font-size="16" fill="#333333">内容示例：联通FTTO网络平均时延（如长沙本地&lt;5ms）</text>
      <text x="30" y="95" font-family="Microsoft YaHei" font-size="16" fill="#333333">平均丢包率（如&lt;0.01%）、SLA达成率（如99.99%）</text>
      <text x="30" y="115" font-family="Microsoft YaHei" font-size="16" fill="#333333">应用：证明联通网络的技术领先性和服务可靠性</text>
      
      <!-- 客户方数据 -->
      <rect x="440" y="0" width="420" height="160" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <text x="470" y="30" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#4ECDC4">客户方数据（引导客户共同测算）</text>
      <text x="470" y="55" font-family="Microsoft YaHei" font-size="16" fill="#333333">来源：与客户IT、财务、业务部门访谈</text>
      <text x="470" y="75" font-family="Microsoft YaHei" font-size="16" fill="#333333">内容示例：当前网络月租/年费、IT运维人力投入</text>
      <text x="470" y="95" font-family="Microsoft YaHei" font-size="16" fill="#333333">因网络问题导致的平均业务中断时长/频率</text>
      <text x="470" y="115" font-family="Microsoft YaHei" font-size="16" fill="#333333">应用：作为计算成本节省和效率提升的基础数据</text>
      
      <!-- 第三方权威数据 -->
      <rect x="880" y="0" width="420" height="160" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <text x="910" y="30" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#45B7D1">第三方权威数据（增强客观性）</text>
      <text x="910" y="55" font-family="Microsoft YaHei" font-size="16" fill="#333333">来源：政府统计部门、行业协会报告、权威咨询机构</text>
      <text x="910" y="75" font-family="Microsoft YaHei" font-size="16" fill="#333333">内容示例：长沙市企业平均上云成本</text>
      <text x="910" y="95" font-family="Microsoft YaHei" font-size="16" fill="#333333">XX行业平均IT运维支出占比</text>
      <text x="910" y="115" font-family="Microsoft YaHei" font-size="16" fill="#333333">应用：为我方方案的效益预测提供行业参照和佐证</text>
      
      <!-- 竞品对比数据 -->
      <rect x="1320" y="0" width="420" height="160" rx="15" fill="white" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
      <text x="1350" y="30" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#96CEB4">竞品对比数据（需客观公正）</text>
      <text x="1350" y="55" font-family="Microsoft YaHei" font-size="16" fill="#333333">来源：公开宣传资料、客户反馈、第三方评测报告</text>
      <text x="1350" y="75" font-family="Microsoft YaHei" font-size="16" fill="#333333">内容示例：在对称带宽、关键SLA指标、TCO等方面</text>
      <text x="1350" y="95" font-family="Microsoft YaHei" font-size="16" fill="#333333">与主要竞争对手进行对比（突出我方优势）</text>
      <text x="1350" y="115" font-family="Microsoft YaHei" font-size="16" fill="#333333">应用：差异化价值呈现，应对客户的竞品比较</text>
    </g>
  </g>
  
  <!-- 右侧装饰图形 -->
  <g transform="translate(1650, 500)" opacity="0.4">
    <circle cx="0" cy="0" r="60" fill="none" stroke="#0066cc" stroke-width="3"/>
    <rect x="-25" y="-30" width="50" height="15" rx="3" fill="#FF6B6B" opacity="0.6"/>
    <rect x="-25" y="-10" width="50" height="15" rx="3" fill="#4ECDC4" opacity="0.6"/>
    <rect x="-25" y="10" width="50" height="15" rx="3" fill="#45B7D1" opacity="0.6"/>
    <rect x="-25" y="30" width="50" height="15" rx="3" fill="#96CEB4" opacity="0.6"/>
    <circle cx="0" cy="0" r="8" fill="#0066cc" opacity="0.8"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="8" fill="white">数据</text>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 0 960 Q 960 1000 1920 960 L 1920 1080 L 0 1080 Z" fill="#0066cc" opacity="0.05"/>
</svg>
